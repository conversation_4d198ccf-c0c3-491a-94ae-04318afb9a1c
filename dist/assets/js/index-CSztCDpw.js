const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/page-home-BOkLa380.js","assets/js/react-vendor-Ba7ko1lq.js","assets/js/vendor-CRi0sl0h.js","assets/css/react-vendor-D-Ncpkvi.css","assets/js/utils-BSH-sDAu.js","assets/js/supabase-vendor-frH3orJu.js","assets/js/components-BPfA4kAc.js","assets/js/page-post-DHESiHI8.js","assets/js/page-authors-B38NLlg-.js","assets/js/page-author-DMcRfL_m.js","assets/js/page-category-BxPP0BYJ.js","assets/js/page-tag-DXSKsLMi.js","assets/js/AdminLayout-BLtk3AxC.js","assets/js/CreatePost-akMRYY7f.js","assets/js/EditPost-DmAUIKXC.js","assets/js/PostsList-DpIxUnMl.js"])))=>i.map(i=>d[i]);
import{r,j as e,B as p,e as m,f as s,O as _,h}from"./react-vendor-Ba7ko1lq.js";import{_ as i}from"./vendor-CRi0sl0h.js";import{H as f,S as x,F as j}from"./components-BPfA4kAc.js";import{w as y,x as E}from"./utils-BSH-sDAu.js";import"./supabase-vendor-frH3orJu.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const t of document.querySelectorAll('link[rel="modulepreload"]'))c(t);new MutationObserver(t=>{for(const o of t)if(o.type==="childList")for(const u of o.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&c(u)}).observe(document,{childList:!0,subtree:!0});function l(t){const o={};return t.integrity&&(o.integrity=t.integrity),t.referrerPolicy&&(o.referrerPolicy=t.referrerPolicy),t.crossOrigin==="use-credentials"?o.credentials="include":t.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function c(t){if(t.ep)return;t.ep=!0;const o=l(t);fetch(t.href,o)}})();const g=r.lazy(()=>i(()=>import("./page-home-BOkLa380.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),P=r.lazy(()=>i(()=>import("./page-post-DHESiHI8.js"),__vite__mapDeps([7,1,2,3,4,5,6]))),O=r.lazy(()=>i(()=>import("./page-authors-B38NLlg-.js"),__vite__mapDeps([8,1,2,3,4,5]))),L=r.lazy(()=>i(()=>import("./page-author-DMcRfL_m.js"),__vite__mapDeps([9,1,2,3,4,5,6]))),A=r.lazy(()=>i(()=>import("./page-category-BxPP0BYJ.js"),__vite__mapDeps([10,1,2,3,4,5,6]))),R=r.lazy(()=>i(()=>import("./page-tag-DXSKsLMi.js"),__vite__mapDeps([11,1,2,3,4,5,6]))),v=r.lazy(()=>i(()=>import("./AdminLayout-BLtk3AxC.js"),__vite__mapDeps([12,1,2,3]))),S=r.lazy(()=>i(()=>import("./CreatePost-akMRYY7f.js"),__vite__mapDeps([13,1,2,3,6,4,5]))),I=r.lazy(()=>i(()=>import("./EditPost-DmAUIKXC.js"),__vite__mapDeps([14,1,2,3,6,4,5]))),T=r.lazy(()=>i(()=>import("./PostsList-DpIxUnMl.js"),__vite__mapDeps([15,1,2,3,4,5,6]))),d=r.memo(()=>{const[a,n]=r.useState(""),l=r.useCallback(t=>{n(t)},[]),c=r.useCallback(t=>{n(t)},[]);return e.jsxs(p,{children:[e.jsxs("div",{className:"container",children:[e.jsx(f,{onSearch:l,searchQuery:a,setSearchQuery:c}),e.jsx(r.Suspense,{fallback:e.jsx(x,{type:"post",count:3}),children:e.jsxs(m,{children:[e.jsx(s,{path:"/",element:e.jsx(g,{searchQuery:a})}),e.jsx(s,{path:"/authors",element:e.jsx(O,{searchQuery:a})}),e.jsx(s,{path:"/author/:username",element:e.jsx(L,{searchQuery:a})}),e.jsx(s,{path:"/category/:slug",element:e.jsx(A,{searchQuery:a})}),e.jsx(s,{path:"/tag/:slug",element:e.jsx(R,{searchQuery:a})}),e.jsxs(s,{path:"/admin",element:e.jsx(v,{}),children:[e.jsx(s,{path:"posts",element:e.jsx(T,{})}),e.jsx(s,{path:"create",element:e.jsx(S,{})}),e.jsx(s,{path:"edit/:id",element:e.jsx(I,{})})]}),e.jsx(s,{path:"/:slug",element:e.jsx(P,{})})]})}),e.jsx(j,{})]}),e.jsx(_,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,theme:{primary:"#4aed88"}}}})]})});d.displayName="App";h.createRoot(document.getElementById("root")).render(e.jsx(r.StrictMode,{children:e.jsx(d,{})}));y();E({onSuccess:()=>{},onUpdate:()=>{}});
