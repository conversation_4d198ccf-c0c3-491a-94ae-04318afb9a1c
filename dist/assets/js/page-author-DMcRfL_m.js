var h=(n,a,r)=>new Promise((m,e)=>{var p=o=>{try{i(r.next(o))}catch(d){e(d)}},u=o=>{try{i(r.throw(o))}catch(d){e(d)}},i=o=>o.done?m(o.value):Promise.resolve(o.value).then(p,u);i((r=r.apply(n,a)).next())});import{b as j,r as l,j as t}from"./react-vendor-Ba7ko1lq.js";import{j as y,k as b}from"./utils-BSH-sDAu.js";import{P as A}from"./components-BPfA4kAc.js";import"./vendor-CRi0sl0h.js";import"./supabase-vendor-frH3orJu.js";const _=({searchQuery:n})=>{const{username:a}=j(),[r,m]=l.useState([]),[e,p]=l.useState(null),[u,i]=l.useState(!0),[o,d]=l.useState(null);l.useEffect(()=>{a&&x()},[a,n]);const x=()=>h(null,null,function*(){try{i(!0);const s=yield y(a);if(!s)throw new Error("Author not found");p(s);const c=yield b(s.id,n);m(c)}catch(s){d("Author not found")}finally{i(!1)}}),g=s=>s?s.split(" ").map(c=>c.charAt(0)).join("").toUpperCase().substring(0,2):"A",f=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return u?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"loading",children:"Loading author..."})}):o||!e?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"error",children:o||"Author not found"})}):t.jsxs(t.Fragment,{children:[e&&t.jsxs("div",{style:{textAlign:"center",marginBottom:"30px",padding:"40px 20px",background:"white",border:"1px solid #f0f0f0"},children:[t.jsx("div",{style:{width:"80px",height:"80px",background:"#ddd",borderRadius:"50%",margin:"0 auto 20px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"32px",fontWeight:"bold",color:"#666"},children:g(e.display_name)}),t.jsx("h1",{style:{fontSize:"32px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:e.display_name}),t.jsxs("p",{style:{color:"#666",fontSize:"16px",marginBottom:"10px"},children:["@",e.user_login]}),t.jsxs("p",{style:{color:"#999",fontSize:"14px",marginBottom:"10px"},children:["Member since ",f(e.user_registered)]}),t.jsxs("p",{style:{color:"#999",fontSize:"14px"},children:[r.length," ",r.length===1?"post":"posts"," published"]})]}),r.length===0?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"loading",children:n?`No posts found by ${e==null?void 0:e.display_name} for "${n}"`:`No posts found by ${e==null?void 0:e.display_name}`})}):t.jsx("div",{className:"main-grid",children:r.map(s=>t.jsx(A,{post:s},s.id))})]})};export{_ as default};
