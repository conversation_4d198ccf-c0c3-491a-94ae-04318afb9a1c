var p=(r,l,e)=>new Promise((m,i)=>{var d=a=>{try{n(e.next(a))}catch(s){i(s)}},h=a=>{try{n(e.throw(a))}catch(s){i(s)}},n=a=>a.done?m(a.value):Promise.resolve(a.value).then(d,h);n((e=e.apply(r,l)).next())});import{r as u,j as t,L as f}from"./react-vendor-Ba7ko1lq.js";import{l as x}from"./utils-BSH-sDAu.js";import"./vendor-CRi0sl0h.js";import"./supabase-vendor-frH3orJu.js";const L=({searchQuery:r})=>{const[l,e]=u.useState([]),[m,i]=u.useState(!0),[d,h]=u.useState(null);u.useEffect(()=>{n()},[r]);const n=()=>p(null,null,function*(){try{i(!0);let s=yield x();if(r&&r.trim()){const o=r.toLowerCase();s=s.filter(c=>c.display_name.toLowerCase().includes(o)||c.username.toLowerCase().includes(o))}s.sort((o,c)=>(c.post_count||0)-(o.post_count||0)),e(s)}catch(s){h("Failed to load authors")}finally{i(!1)}}),a=s=>s?s.split(" ").map(o=>o.charAt(0)).join("").toUpperCase().substring(0,2):"A";return m?t.jsx("div",{className:"author-grid",children:t.jsx("div",{className:"loading",children:"Loading authors..."})}):d?t.jsx("div",{className:"author-grid",children:t.jsx("div",{className:"error",children:d})}):l.length===0?t.jsx("div",{className:"author-grid",children:t.jsx("div",{className:"loading",children:r?`No authors found for "${r}"`:"No authors available"})}):t.jsx("div",{className:"author-grid",children:l.map(s=>t.jsxs(f,{to:`/author/${s.username}`,className:"author-card",children:[t.jsx("div",{className:"author-avatar",children:a(s.display_name)}),t.jsx("div",{className:"author-name",children:s.display_name}),t.jsxs("div",{className:"author-count",children:[s.post_count," ",s.post_count===1?"post":"posts"]})]},s.id))})};export{L as default};
