var p=(i,a,e)=>new Promise((c,s)=>{var m=o=>{try{n(e.next(o))}catch(l){s(l)}},g=o=>{try{n(e.throw(o))}catch(l){s(l)}},n=o=>o.done?c(o.value):Promise.resolve(o.value).then(m,g);n((e=e.apply(i,a)).next())});import{b as u,r as d,j as t}from"./react-vendor-Ba7ko1lq.js";import{m as h,n as j}from"./utils-BSH-sDAu.js";import{P as y}from"./components-BPfA4kAc.js";import"./vendor-CRi0sl0h.js";import"./supabase-vendor-frH3orJu.js";const E=({searchQuery:i})=>{const{slug:a}=u(),[e,c]=d.useState([]),[s,m]=d.useState(null),[g,n]=d.useState(!0),[o,l]=d.useState(null);d.useEffect(()=>{a&&x()},[a,i]);const x=()=>p(null,null,function*(){try{n(!0);const r=yield h(a);if(!r)throw new Error("Category not found");m(r);const f=yield j(r.id,i);c(f)}catch(r){l("Failed to load category posts")}finally{n(!1)}});return g?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"loading",children:"Loading posts..."})}):o?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"error",children:o})}):e.length===0?t.jsx("div",{className:"main-grid",children:t.jsx("div",{className:"loading",children:i?`No posts found in "${s==null?void 0:s.name}" for "${i}"`:`No posts found in "${s==null?void 0:s.name}"`})}):t.jsxs(t.Fragment,{children:[s&&t.jsxs("div",{style:{textAlign:"center",marginBottom:"30px",padding:"20px",background:"white",border:"1px solid #f0f0f0"},children:[t.jsx("h1",{style:{fontSize:"28px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:s.name}),s.description&&t.jsx("p",{style:{color:"#666",fontSize:"16px"},children:s.description}),t.jsxs("p",{style:{color:"#999",fontSize:"14px",marginTop:"10px"},children:[e.length," ",e.length===1?"post":"posts"]})]}),t.jsx("div",{className:"main-grid",children:e.map(r=>t.jsx(y,{post:r},r.id))})]})};export{E as default};
