var v=(o,l,n)=>new Promise((m,g)=>{var c=a=>{try{i(n.next(a))}catch(d){g(d)}},p=a=>{try{i(n.throw(a))}catch(d){g(d)}},i=a=>a.done?m(a.value):Promise.resolve(a.value).then(c,p);i((n=n.apply(o,l)).next())});import{b as $,r,j as e,L as h}from"./react-vendor-Ba7ko1lq.js";import{p as D,q as L}from"./utils-BSH-sDAu.js";import"./components-BPfA4kAc.js";import"./vendor-CRi0sl0h.js";import"./supabase-vendor-frH3orJu.js";const R=()=>{const{slug:o}=$(),[l,n]=r.useState(null),[m,g]=r.useState([]),[c,p]=r.useState([]),[i,a]=r.useState([]),[d,u]=r.useState(!0),[f,y]=r.useState(null);r.useEffect(()=>{o&&N()},[o]);const N=()=>v(null,null,function*(){try{u(!0);const s=yield D(o);if(!s)throw new Error("Post not found");n(s);const j=(yield L()).filter(x=>x.id!==s.id).slice(0,3);g(j),p(s.categories||[]),a(s.tags||[])}catch(s){y("Post not found")}finally{u(!1)}}),b=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),P=s=>{if(!s)return"";let t=s.replace(/<!-- wp:.*? -->/g,"");return t=t.replace(/<!-- \/wp:.*? -->/g,""),t=t.replace(/class="[^"]*wp-[^"]*"/g,""),t=t.replace(/class="[^"]*has-[^"]*"/g,""),t=t.replace(/style="[^"]*text-transform:[^"]*"/g,""),t=t.replace(/<img([^>]*)src="([^"]*)"([^>]*)>/g,(j,x,S,w)=>`<img${x}src="${S}"${w} loading="lazy" decoding="async" style="max-width: 100%; height: auto; margin: 20px 0; border-radius: 8px;" sizes="(max-width: 768px) 100vw, 800px">`),t=t.replace(/<h([1-6])([^>]*)>/g,'<h$1$2 style="margin: 30px 0 20px 0;">'),t=t.replace(/<blockquote([^>]*)>/g,'<blockquote$1 style="margin: 20px 0; padding: 15px 20px; border-left: 4px solid #ddd; background: #f9f9f9; font-style: italic;">'),t.trim()};return d?e.jsx("div",{className:"single-poem-grid",children:e.jsx("div",{className:"loading",children:"Loading post..."})}):f||!l?e.jsx("div",{className:"single-poem-grid",children:e.jsx("div",{className:"error",children:f||"Post not found"})}):e.jsxs("div",{className:"single-poem-grid",children:[e.jsxs("div",{className:"poem-content",children:[e.jsx("div",{className:"poem-full-title",children:l.title}),e.jsxs("div",{className:"poem-meta",style:{marginBottom:"30px"},children:[e.jsx("div",{className:"author",children:"By Admin"}),e.jsx("div",{className:"date",style:{marginLeft:"20px"},children:b(l.published_at)})]}),e.jsx("div",{className:"poem-text",dangerouslySetInnerHTML:{__html:P(l.content)}})]}),e.jsxs("div",{className:"poem-sidebar",children:[c.length>0&&e.jsxs("div",{className:"sidebar-section",children:[e.jsx("div",{className:"sidebar-title",children:"Categories"}),e.jsx("div",{className:"sidebar-content",children:c.map((s,t)=>e.jsxs("span",{children:[e.jsx(h,{to:`/category/${s.slug}`,style:{color:"#666",textDecoration:"none"},children:s.name}),t<c.length-1&&", "]},s.id))})]}),i.length>0&&e.jsxs("div",{className:"sidebar-section",children:[e.jsx("div",{className:"sidebar-title",children:"Tags"}),e.jsx("div",{className:"sidebar-content",children:i.map((s,t)=>e.jsxs("span",{children:[e.jsx(h,{to:`/tag/${s.slug}`,style:{color:"#666",textDecoration:"none"},children:s.name}),t<i.length-1&&", "]},s.id))})]}),m.length>0&&e.jsxs("div",{className:"sidebar-section",children:[e.jsx("div",{className:"sidebar-title",children:"Related Posts"}),e.jsx("div",{className:"sidebar-content",children:m.map(s=>e.jsx("div",{style:{marginBottom:"10px"},children:e.jsx(h,{to:`/${s.slug}`,style:{color:"#666",textDecoration:"none",fontSize:"13px",display:"block"},children:s.title})},s.id))})]})]})]})};export{R as default};
