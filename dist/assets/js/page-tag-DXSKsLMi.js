var p=(a,n,e)=>new Promise((c,t)=>{var g=o=>{try{r(e.next(o))}catch(l){t(l)}},m=o=>{try{r(e.throw(o))}catch(l){t(l)}},r=o=>o.done?c(o.value):Promise.resolve(o.value).then(g,m);r((e=e.apply(a,n)).next())});import{b as h,r as d,j as s}from"./react-vendor-Ba7ko1lq.js";import{r as u,t as j}from"./utils-BSH-sDAu.js";import{P as N}from"./components-BPfA4kAc.js";import"./vendor-CRi0sl0h.js";import"./supabase-vendor-frH3orJu.js";const E=({searchQuery:a})=>{const{slug:n}=h(),[e,c]=d.useState([]),[t,g]=d.useState(null),[m,r]=d.useState(!0),[o,l]=d.useState(null);d.useEffect(()=>{n&&x()},[n,a]);const x=()=>p(null,null,function*(){try{r(!0);const i=yield u(n);if(!i)throw new Error("Tag not found");g(i);const f=yield j(i.id,a);c(f)}catch(i){l("Failed to load tag posts")}finally{r(!1)}});return m?s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"loading",children:"Loading posts..."})}):o?s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"error",children:o})}):e.length===0?s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"loading",children:a?`No posts found with tag "${t==null?void 0:t.name}" for "${a}"`:`No posts found with tag "${t==null?void 0:t.name}"`})}):s.jsxs(s.Fragment,{children:[t&&s.jsxs("div",{style:{textAlign:"center",marginBottom:"30px",padding:"20px",background:"white",border:"1px solid #f0f0f0"},children:[s.jsxs("h1",{style:{fontSize:"28px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:["#",t.name]}),t.description&&s.jsx("p",{style:{color:"#666",fontSize:"16px"},children:t.description}),s.jsxs("p",{style:{color:"#999",fontSize:"14px",marginTop:"10px"},children:[e.length," ",e.length===1?"post":"posts"]})]}),s.jsx("div",{className:"main-grid",children:e.map(i=>s.jsx(N,{post:i},i.id))})]})};export{E as default};
