var i=(r,e,t)=>new Promise((a,s)=>{var o=n=>{try{l(t.next(n))}catch(d){s(d)}},c=n=>{try{l(t.throw(n))}catch(d){s(d)}},l=n=>n.done?a(n.value):Promise.resolve(n.value).then(o,c);l((t=t.apply(r,e)).next())});import{c as P}from"./supabase-vendor-frH3orJu.js";import{s as _}from"./vendor-CRi0sl0h.js";const D=(r,e=150)=>{if(!r)return"";const t=r.replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").trim();if(t.length<=e)return t;const a=t.substring(0,e),s=a.lastIndexOf(" ");return s>e*.8?a.substring(0,s)+"...":a+"..."},A=(r,e=null)=>{if(e)return e;if(!r)return null;const t=r.match(/<img[^>]+src="([^"]*)"[^>]*>/i);return t?t[1]:null},J=r=>{if(!r)return"";const e=new Date(r),a=Math.abs(new Date-e),s=Math.ceil(a/(1e3*60*60*24));return s===1?"Yesterday":s<7?`${s} days ago`:s<30?`${Math.ceil(s/7)} weeks ago`:e.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})},N=(r,e)=>{let t;return function(...s){const o=()=>{clearTimeout(t),r(...s)};clearTimeout(t),t=setTimeout(o,e)}},C=!!(window.location.hostname==="localhost"||window.location.hostname==="[::1]"||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function R(r){if("serviceWorker"in navigator){if(new URL("/",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",()=>{const t="/sw.js";C?(I(t,r),navigator.serviceWorker.ready.then(()=>{})):E(t,r)})}}function E(r,e){navigator.serviceWorker.register(r).then(t=>{t.onupdatefound=()=>{const a=t.installing;a!=null&&(a.onstatechange=()=>{a.state==="installed"&&(navigator.serviceWorker.controller?e&&e.onUpdate&&e.onUpdate(t):e&&e.onSuccess&&e.onSuccess(t))})}}).catch(t=>{})}function I(r,e){fetch(r,{headers:{"Service-Worker":"script"}}).then(t=>{const a=t.headers.get("content-type");t.status===404||a!=null&&a.indexOf("javascript")===-1?navigator.serviceWorker.ready.then(s=>{s.unregister().then(()=>{window.location.reload()})}):E(r,e)}).catch(()=>{})}class L{constructor(){this.metrics={},this.observers=[],this.init()}init(){this.trackLCP(),this.trackFID(),this.trackCLS(),this.trackFCP(),this.trackTTFB(),this.trackNavigationTiming(),this.trackResourceTiming()}trackLCP(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{const a=t.getEntries(),s=a[a.length-1];this.metrics.lcp={value:s.startTime,element:s.element,timestamp:Date.now()},this.reportMetric("LCP",s.startTime)});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}trackFID(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{t.getEntries().forEach(s=>{this.metrics.fid={value:s.processingStart-s.startTime,timestamp:Date.now()},this.reportMetric("FID",s.processingStart-s.startTime)})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}trackCLS(){if("PerformanceObserver"in window){let e=0;const t=new PerformanceObserver(a=>{a.getEntries().forEach(o=>{o.hadRecentInput||(e+=o.value)}),this.metrics.cls={value:e,timestamp:Date.now()},this.reportMetric("CLS",e)});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}}trackFCP(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{t.getEntries().forEach(s=>{s.name==="first-contentful-paint"&&(this.metrics.fcp={value:s.startTime,timestamp:Date.now()},this.reportMetric("FCP",s.startTime))})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}}trackTTFB(){if("performance"in window&&"getEntriesByType"in performance){const e=performance.getEntriesByType("navigation");if(e.length>0){const t=e[0],a=t.responseStart-t.requestStart;this.metrics.ttfb={value:a,timestamp:Date.now()},this.reportMetric("TTFB",a)}}}trackNavigationTiming(){if("performance"in window&&"getEntriesByType"in performance){const e=performance.getEntriesByType("navigation");if(e.length>0){const t=e[0];this.metrics.navigation={domContentLoaded:t.domContentLoadedEventEnd-t.domContentLoadedEventStart,loadComplete:t.loadEventEnd-t.loadEventStart,domInteractive:t.domInteractive-t.navigationStart,timestamp:Date.now()}}}}trackResourceTiming(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{t.getEntries().forEach(s=>{s.initiatorType==="img"&&this.trackImageLoad(s)})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}trackImageLoad(e){const t=e.responseEnd-e.startTime;this.metrics.images||(this.metrics.images=[]),this.metrics.images.push({url:e.name,loadTime:t,size:e.transferSize,timestamp:Date.now()}),t>1e3}reportMetric(e,t){window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t)})}getMetrics(){return this.metrics}getPerformanceScore(){const{lcp:e,fid:t,cls:a,fcp:s}=this.metrics;let o=100;return e&&(e.value>4e3?o-=30:e.value>2500&&(o-=15)),t&&(t.value>300?o-=25:t.value>100&&(o-=10)),a&&(a.value>.25?o-=25:a.value>.1&&(o-=10)),s&&(s.value>3e3?o-=20:s.value>1800&&(o-=10)),Math.max(0,o)}disconnect(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}}const z=()=>{if(typeof window!="undefined"){const r=new L;return window.addEventListener("load",()=>{setTimeout(()=>{const e=r.getPerformanceScore()},5e3)}),r}return null},F="https://ckjpejxjpcfmlyopqabt.supabase.co",q="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNranBlanhqcGNmbWx5b3BxYWJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MTk5OTUsImV4cCI6MjA2OTI5NTk5NX0.sdinJPYznrITCIJBijRV2iwA0TSLLsTmLWtTYY37OLE",g=P(F,q,{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"sayari-blog@1.0.0"}}});let u={posts:null,authors:null,categories:null,tags:null};function v(r){return i(this,null,function*(){try{const e=yield fetch(`/data/static/${r}`);if(!e.ok)throw new Error(`Failed to load ${r}: ${e.status}`);return yield e.json()}catch(e){return null}})}function b(){return i(this,null,function*(){if(u.posts||(u.posts=yield v("posts.json")),u.posts)return u.posts;const{data:r,error:e}=yield g.from("posts").select(`
      id,
      title,
      slug,
      content,
      excerpt,
      author_id,
      published_at,
      status,
      featured_image_url,
      authors:author_id (
        id,
        username,
        display_name,
        bio
      )
    `).eq("status","published").order("published_at",{ascending:!1});if(e)throw new Error(`Failed to fetch posts: ${e.message}`);return r||[]})}function Y(r){return i(this,null,function*(){return(yield b()).find(t=>t.slug===r)||null})}function X(r=0,e=10,t=""){return i(this,null,function*(){const a=yield b();let s=a;if(t&&t.trim()){const n=t.toLowerCase();s=a.filter(d=>{var f,m;return d.title.toLowerCase().includes(n)||((f=d.excerpt)==null?void 0:f.toLowerCase().includes(n))||((m=d.content)==null?void 0:m.toLowerCase().includes(n))})}const o=r*e,c=o+e;return{posts:s.slice(o,c),hasMore:c<s.length,total:s.length}})}function $(){return i(this,null,function*(){if(u.authors||(u.authors=yield v("authors.json")),u.authors)return u.authors;const{data:r,error:e}=yield g.from("users").select("id, user_login, display_name, user_registered").order("display_name");if(e)throw new Error(`Failed to fetch authors: ${e.message}`);return r||[]})}function Z(r){return i(this,null,function*(){return(yield $()).find(t=>t.username===r)||null})}function G(r,e=""){return i(this,null,function*(){let a=(yield b()).filter(s=>s.author_id===r);if(e&&e.trim()){const s=e.toLowerCase();a=a.filter(o=>{var c,l;return o.title.toLowerCase().includes(s)||((c=o.excerpt)==null?void 0:c.toLowerCase().includes(s))||((l=o.content)==null?void 0:l.toLowerCase().includes(s))})}return a})}function O(){return i(this,null,function*(){if(u.categories||(u.categories=yield v("categories.json")),u.categories)return u.categories;const{data:r,error:e}=yield g.from("categories").select("id, name, slug, description").order("name");if(e)throw new Error(`Failed to fetch categories: ${e.message}`);return r||[]})}function H(r){return i(this,null,function*(){return(yield O()).find(t=>t.slug===r)||null})}function K(r,e=""){return i(this,null,function*(){let a=(yield b()).filter(s=>s.categories&&s.categories.some(o=>o.id===r));if(e&&e.trim()){const s=e.toLowerCase();a=a.filter(o=>{var c,l;return o.title.toLowerCase().includes(s)||((c=o.excerpt)==null?void 0:c.toLowerCase().includes(s))||((l=o.content)==null?void 0:l.toLowerCase().includes(s))})}return a})}function M(){return i(this,null,function*(){if(u.tags||(u.tags=yield v("tags.json")),u.tags)return u.tags;const{data:r,error:e}=yield g.from("tags").select("id, name, slug, description").order("name");if(e)throw new Error(`Failed to fetch tags: ${e.message}`);return r||[]})}function Q(r){return i(this,null,function*(){return(yield M()).find(t=>t.slug===r)||null})}function ee(r,e=""){return i(this,null,function*(){let a=(yield b()).filter(s=>s.tags&&s.tags.some(o=>o.id===r));if(e&&e.trim()){const s=e.toLowerCase();a=a.filter(o=>{var c,l;return o.title.toLowerCase().includes(s)||((c=o.excerpt)==null?void 0:c.toLowerCase().includes(s))||((l=o.content)==null?void 0:l.toLowerCase().includes(s))})}return a})}const te=(t,...a)=>i(null,[t,...a],function*(r,e={}){const{maxWidth:s=1200,maxHeight:o=800,quality:c=.8,format:l="image/jpeg"}=e;return new Promise((n,d)=>{const f=document.createElement("canvas"),m=f.getContext("2d"),w=new Image;w.onload=()=>{let{width:h,height:p}=w;h>s&&(p=p*s/h,h=s),p>o&&(h=h*o/p,p=o),f.width=h,f.height=p,m.drawImage(w,0,0,h,p),f.toBlob(y=>{if(y){const k=new File([y],r.name,{type:l,lastModified:Date.now()});n(k)}else d(new Error("Failed to compress image"))},l,c)},w.onerror=()=>{d(new Error("Failed to load image"))},w.src=URL.createObjectURL(r)})}),x={अ:"a",आ:"aa",इ:"i",ई:"ii",उ:"u",ऊ:"uu",ए:"e",ऐ:"ai",ओ:"o",औ:"au",क:"ka",ख:"kha",ग:"ga",घ:"gha",ङ:"nga",च:"cha",छ:"chha",ज:"ja",झ:"jha",ञ:"nya",ट:"ta",ठ:"tha",ड:"da",ढ:"dha",ण:"na",त:"ta",थ:"tha",द:"da",ध:"dha",न:"na",प:"pa",फ:"pha",ब:"ba",भ:"bha",म:"ma",य:"ya",र:"ra",ल:"la",व:"va",श:"sha",ष:"sha",स:"sa",ह:"ha",क्ष:"ksha",त्र:"tra",ज्ञ:"gya","ं":"n","ः":"h","्":"","ा":"aa","ि":"i","ी":"ii","ु":"u","ू":"uu","े":"e","ै":"ai","ो":"o","ौ":"au"},B=r=>{if(!r)return"";let e=r;return Object.entries(x).forEach(([t,a])=>{const s=new RegExp(t,"g");e=e.replace(s,a)}),e},re=r=>{if(!r)return"";const e=B(r),t=_(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g});return t?t.substring(0,100):`post-${Date.now()}`},j=(r,e=null)=>i(null,null,function*(){if(!r)return!1;try{let t=g.from("posts").select("id").eq("slug",r);e&&(t=t.neq("id",e));const{data:a,error:s}=yield t;return s?!1:!a||a.length===0}catch(t){return!1}}),S=(r,e=null)=>i(null,null,function*(){let t=r,a=1;for(;!(yield j(t,e));)if(t=`${r}-${a}`,a++,a>100){t=`${r}-${Date.now()}`;break}return t}),T=r=>r?r.length<3?{isValid:!1,message:"Slug must be at least 3 characters long"}:r.length>100?{isValid:!1,message:"Slug must be less than 100 characters"}:/^[a-z0-9-]+$/.test(r)?r.startsWith("-")||r.endsWith("-")?{isValid:!1,message:"Slug cannot start or end with a hyphen"}:r.includes("--")?{isValid:!1,message:"Slug cannot contain consecutive hyphens"}:["admin","api","www","mail","ftp","localhost","test","staging","dev","development","prod","production","blog","post","page","category","tag","author","search"].includes(r)?{isValid:!1,message:"This slug is reserved and cannot be used"}:{isValid:!0,message:""}:{isValid:!1,message:"Slug can only contain lowercase letters, numbers, and hyphens"}:{isValid:!1,message:"Slug is required"},se=r=>i(null,null,function*(){try{if(!r.title)throw new Error("Title is required");const e=T(r.slug);if(!e.isValid)throw new Error(e.message);const t=yield S(r.slug),a={title:r.title.trim(),slug:t,content:r.content||"",excerpt:r.excerpt||"",status:r.status||"draft",featured_image_url:r.featured_image_url||null,meta_title:r.meta_title||null,meta_description:r.meta_description||null,author_id:null,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};a.status==="published"&&(a.published_at=r.published_at||new Date().toISOString());const{data:s,error:o}=yield g.from("posts").insert([a]).select().single();if(o)throw new Error(`Failed to create post: ${o.message}`);return s}catch(e){throw e}}),ae=(r,e)=>i(null,null,function*(){try{if(!r)throw new Error("Post ID is required");if(!e.title)throw new Error("Title is required");if(e.slug){const o=T(e.slug);if(!o.isValid)throw new Error(o.message);const c=yield S(e.slug,r);e.slug=c}const t={title:e.title.trim(),content:e.content||"",excerpt:e.excerpt||"",status:e.status||"draft",featured_image_url:e.featured_image_url||null,meta_title:e.meta_title||null,meta_description:e.meta_description||null,updated_at:new Date().toISOString()};if(e.slug&&(t.slug=e.slug),t.status==="published"&&e.published_at)t.published_at=e.published_at;else if(t.status==="published"){const{data:o}=yield g.from("posts").select("published_at").eq("id",r).single();o!=null&&o.published_at||(t.published_at=new Date().toISOString())}const{data:a,error:s}=yield g.from("posts").update(t).eq("id",r).select().single();if(s)throw new Error(`Failed to update post: ${s.message}`);return a}catch(t){throw t}}),oe=r=>i(null,null,function*(){try{if(!r)throw new Error("Post ID is required");const{error:e}=yield g.from("posts").delete().eq("id",r);if(e)throw new Error(`Failed to delete post: ${e.message}`);return!0}catch(e){throw e}}),ne=(...e)=>i(null,[...e],function*(r={}){try{const{page:t=0,limit:a=10,status:s=null,search:o="",orderBy:c="updated_at",orderDirection:l="desc"}=r;let n=g.from("posts").select("id, title, slug, status, published_at, created_at, updated_at",{count:"exact"});s&&s!=="all"&&(n=n.eq("status",s)),o&&o.trim()&&(n=n.ilike("title",`%${o.trim()}%`)),n=n.order(c,{ascending:l==="asc"});const d=t*a,f=d+a-1;n=n.range(d,f);const{data:m,error:w,count:h}=yield n;if(w)throw new Error(`Failed to fetch posts: ${w.message}`);return{posts:m||[],total:h||0,page:t,limit:a,hasMore:m&&m.length===a}}catch(t){throw t}}),ie=(r,e)=>i(null,null,function*(){try{if(!r||r.length===0)throw new Error("Post IDs are required");if(!["draft","published","private"].includes(e))throw new Error("Invalid status");const t={status:e,updated_at:new Date().toISOString()};e==="published"&&(t.published_at=new Date().toISOString());const{data:a,error:s}=yield g.from("posts").update(t).in("id",r).select("id");if(s)throw new Error(`Failed to update posts: ${s.message}`);return a?a.length:0}catch(t){throw t}});export{D as a,re as b,te as c,N as d,se as e,J as f,A as g,ie as h,oe as i,Z as j,G as k,$ as l,H as m,K as n,X as o,Y as p,b as q,Q as r,g as s,ee as t,ae as u,j as v,z as w,R as x,ne as y};
