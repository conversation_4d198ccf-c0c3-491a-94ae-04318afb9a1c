<!doctype html>
<html lang="hi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>Breakup Shayari In Hindi - Sayari Blog</title>
    <meta name="title" content="Sayari - Hindi Shayari, Quotes & Wishes" />
    <meta name="description" content="Discover beautiful Hindi Shayari, inspiring quotes, and heartfelt wishes. A modern platform for poetry lovers and quote enthusiasts." />
    <meta name="keywords" content="hindi shayari, quotes, wishes, poetry, hindi poetry, love shayari, sad shayari" />
    <meta name="author" content="Sayari Blog" />
    <meta name="robots" content="index, follow" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://sayari-blog.pages.dev/" />
    <meta property="og:title" content="Sayari - Hindi Shayari, Quotes & Wishes" />
    <meta property="og:description" content="Discover beautiful Hindi Shayari, inspiring quotes, and heartfelt wishes." />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://sayari-blog.pages.dev/" />
    <meta property="twitter:title" content="Sayari - Hindi Shayari, Quotes & Wishes" />
    <meta property="twitter:description" content="Discover beautiful Hindi Shayari, inspiring quotes, and heartfelt wishes." />
    <meta property="twitter:image" content="/og-image.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/icon-16x16.png" />
    <link rel="apple-touch-icon" href="/icon-192x192.png" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#333333" />
    <meta name="msapplication-TileColor" content="#333333" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://ckjpejxjpcfmlyopqabt.supabase.co" />
    <link rel="dns-prefetch" href="https://ckjpejxjpcfmlyopqabt.supabase.co" />

    <!-- Font optimization -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Critical CSS inlined for faster rendering -->
    <style>
      /* Critical above-the-fold styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        background: #fafafa;
        color: #333;
        line-height: 1.6;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      /* Loading spinner for better perceived performance */
      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 10px;
        padding: 40px;
        text-align: center;
      }

      .spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #333;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <script type="module" crossorigin src="/assets/js/index-CSztCDpw.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/js/vendor-CRi0sl0h.js">
    <link rel="modulepreload" crossorigin href="/assets/js/react-vendor-Ba7ko1lq.js">
    <link rel="modulepreload" crossorigin href="/assets/js/supabase-vendor-frH3orJu.js">
    <link rel="modulepreload" crossorigin href="/assets/js/utils-BSH-sDAu.js">
    <link rel="modulepreload" crossorigin href="/assets/js/components-BPfA4kAc.js">
    <link rel="stylesheet" crossorigin href="/assets/css/react-vendor-D-Ncpkvi.css">
    <link rel="stylesheet" crossorigin href="/assets/css/index-JfeZj3yg.css">
  
    <!-- SEO Meta Tags -->
    <meta name="description" content="Breakup Shayari In Hindi">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://sayari-blog.pages.dev/breakup-shayari">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Breakup Shayari In Hindi - Sayari Blog">
    <meta property="og:description" content="Breakup Shayari In Hindi">
    <meta property="og:url" content="https://sayari-blog.pages.dev/breakup-shayari">
    <meta property="og:type" content="article">
    <meta property="og:image" content="https://ckjpejxjpcfmlyopqabt.supabase.co/storage/v1/object/public/images/1753736771203-tfhnbl-Breakup-Shayari-1-1024x1024.jpg">
    <meta property="og:site_name" content="Sayari Blog">
    <meta property="article:published_time" content="2025-02-18T20:03:26+00:00">
    <meta property="article:author" content="Madhav Jha">
    <meta property="article:tag" content="Breakup Shayari In Hindi, Breakup Shayari Status, Breakup Shayari">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Breakup Shayari In Hindi - Sayari Blog">
    <meta name="twitter:description" content="Breakup Shayari In Hindi">
    <meta name="twitter:image" content="https://ckjpejxjpcfmlyopqabt.supabase.co/storage/v1/object/public/images/1753736771203-tfhnbl-Breakup-Shayari-1-1024x1024.jpg">
    
<script type="application/ld+json">[{"@context":"https://schema.org","@type":"WebSite","name":"Sayari Blog","url":"https://sayari-blog.pages.dev","description":"Hindi Shayari, Quotes & Wishes","potentialAction":{"@type":"SearchAction","target":"https://sayari-blog.pages.dev/?search={search_term_string}","query-input":"required name=search_term_string"}},{"@context":"https://schema.org","@type":"Article","headline":"Breakup Shayari In Hindi","description":"Breakup Shayari In Hindi","url":"https://sayari-blog.pages.dev/breakup-shayari","datePublished":"2025-02-18T20:03:26+00:00","author":{"@type":"Person","name":"Madhav Jha"},"publisher":{"@type":"Organization","name":"Sayari Blog","url":"https://sayari-blog.pages.dev"}}]</script>
</head>
  <body>
    <div id="root"></div>

  </body>
</html>
