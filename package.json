{"name": "sayari-blog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run build:data && npm run build:vite && npm run build:static", "build:data": "node scripts/build-static-data.js", "build:vite": "cross-env SSG_MODE=true vite build", "build:static": "node scripts/generate-static-html.js", "build:spa": "vite build", "preview": "vite preview", "build:analyze": "vite build --mode analyze", "perf:test": "node scripts/performance-test.js", "perf:build": "npm run build && npm run preview & sleep 5 && npm run perf:test && pkill -f 'vite preview'", "test:connection": "node scripts/test-connection.js"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.11.0", "cheerio": "^1.1.2", "cross-env": "^10.0.0", "fs-extra": "^11.3.0", "puppeteer": "^24.15.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vite-plugin-prerender": "^1.0.8", "vite-plugin-static-copy": "^3.1.1"}, "dependencies": {"@supabase/supabase-js": "^2.53.0", "dotenv": "^17.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-quill": "^2.0.0", "react-router-dom": "^7.7.1", "slugify": "^1.6.6"}}